Rencana schema buat API endpoints:

{
  "endpoints": {
    "authentication": {
      "register": {
        "method": "POST",
        "url": "/api/auth/register",
        "body": {
          "username": "string",
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string",
            "profilePictureUrl": "string",
            "createdAt": "timestamp"
          },
          "token": "string"
        }
      },
      "login": {
        "method": "POST",
        "url": "/api/auth/login",
        "body": {
          "email": "string",
          "password": "string"
        },
        "response": {
          "user": {
            "id": "string",
            "username": "string",
            "email": "string",
            "profilePictureUrl": "string"
          },
          "token": "string"
        }
      },
      "logout": {
        "method": "POST",
        "url": "/api/auth/logout",
        "auth": "required"
      },
      "getUser": {
        "method": "GET",
        "url": "/api/auth/user",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "refreshToken": {
        "method": "POST",
        "url": "/api/auth/refresh",
        "body": {
          "refreshToken": "string"
        },
        "response": {
          "token": "string",
          "refreshToken": "string"
        }
      }
    },
    "posts": {
      "getAllPosts": {
        "method": "GET",
        "url": "/api/posts",
        "query": {
          "page": "number",
          "limit": "number",
          "sort": "string"
        }
      },
      "getPostById": {"method": "GET", "url": "/api/posts/{id}"},
      "createPost": {
        "method": "POST",
        "url": "/api/posts",
        "auth": "required",
        "body": {
          "title": "string",
          "content": "string",
          "image": "application/json"
        },
        "response": {
          "id": "string",
          "title": "string",
          "content": "string",
          "imageUrl": "string",
          "userId": "string",
          "createdAt": "timestamp"
        }
      },
      "updatePost": {
        "method": "PUT",
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true,
        "body": {
          "title": "string",
          "content": "string",
          "image": "application/json"
        }
      },
      "deletePost": {
        "method": "DELETE",
        "url": "/api/posts/{id}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "comments": {
      "getComments": {"method": "GET", "url": "/api/posts/{id}/comments"},
      "createComment": {
        "method": "POST",
        "url": "/api/posts/{id}/comments",
        "auth": "required",
        "body": {
          "content": "string"
        },
        "response": {
          "id": "string",
          "content": "string",
          "userId": "string",
          "postId": "string",
          "createdAt": "timestamp"
        }
      },
      "updateComment": {
        "method": "PUT",
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true,
        "body": {
          "content": "string"
        }
      },
      "deleteComment": {
        "method": "DELETE",
        "url": "/api/posts/{id}/comments/{commentId}",
        "auth": "required",
        "ownerOnly": true
      }
    },
    "account": {
      "getAccount": {
        "method": "GET",
        "url": "/api/account",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "updateAccount": {
        "method": "PUT",
        "url": "/api/account",
        "auth": "required",
        "body": {
          "username": "string",
          "email": "string"
        },
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "deleteAccount": {
        "method": "DELETE",
        "url": "/api/account",
        "auth": "required"
      },
      "uploadProfilePicture": {
        "method": "POST",
        "url": "/api/account/profile-picture",
        "auth": "required",
        "body": {
          "profilePicture": "multipart/form-data"
        },
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      },
      "getProfilePicture": {
        "method": "GET",
        "url": "/api/account/profile-picture/{filename}",
        "response": "image/binary"
      },
      "deleteProfilePicture": {
        "method": "DELETE",
        "url": "/api/account/profile-picture",
        "auth": "required",
        "response": {
          "id": "string",
          "username": "string",
          "email": "string",
          "profilePictureUrl": "string",
          "createdAt": "timestamp"
        }
      }
    },
    "categories": {
      "getAllCategories": {
        "method": "GET",
        "url": "/api/categories",
        "query": {
          "active": "boolean"
        },
        "response": {
          "success": "boolean",
          "count": "number",
          "data": [{
            "id": "string",
            "name": "string",
            "description": "string",
            "color": "string",
            "icon": "string",
            "isActive": "boolean",
            "createdAt": "timestamp"
          }]
        }
      },
      "getCategory": {
        "method": "GET",
        "url": "/api/categories/{id}"
      },
      "createCategory": {
        "method": "POST",
        "url": "/api/categories",
        "auth": "required",
        "body": {
          "name": "string",
          "description": "string",
          "color": "string",
          "icon": "string"
        }
      },
      "updateCategory": {
        "method": "PUT",
        "url": "/api/categories/{id}",
        "auth": "required"
      },
      "deleteCategory": {
        "method": "DELETE",
        "url": "/api/categories/{id}",
        "auth": "required"
      }
    },
    "learning": {
      "getAllLearning": {
        "method": "GET",
        "url": "/api/learning",
        "query": {
          "page": "number",
          "limit": "number",
          "sort": "string",
          "category": "string",
          "difficulty": "string",
          "search": "string",
          "published": "boolean"
        },
        "response": {
          "success": "boolean",
          "count": "number",
          "pagination": {
            "total": "number",
            "page": "number",
            "limit": "number",
            "pages": "number"
          },
          "data": [{
            "id": "string",
            "title": "string",
            "content": "string",
            "summary": "string",
            "difficulty": "string",
            "estimatedTime": "number",
            "imageUrl": "string",
            "categories": ["object"],
            "userId": "object",
            "isPublished": "boolean",
            "viewCount": "number",
            "createdAt": "timestamp"
          }]
        }
      },
      "getLearning": {
        "method": "GET",
        "url": "/api/learning/{id}"
      },
      "createLearning": {
        "method": "POST",
        "url": "/api/learning",
        "auth": "required",
        "body": {
          "title": "string",
          "content": "string",
          "summary": "string",
          "difficulty": "string",
          "estimatedTime": "number",
          "categories": "array",
          "image": "multipart/form-data",
          "isPublished": "boolean"
        }
      },
      "updateLearning": {
        "method": "PUT",
        "url": "/api/learning/{id}",
        "auth": "required",
        "ownerOnly": true
      },
      "deleteLearning": {
        "method": "DELETE",
        "url": "/api/learning/{id}",
        "auth": "required",
        "ownerOnly": true
      },
      "getLearningImage": {
        "method": "GET",
        "url": "/api/learning/image/{filename}",
        "response": "image/binary"
      },
      "getLearningByCategory": {
        "method": "GET",
        "url": "/api/learning/category/{categoryId}"
      },
      "bookmarkLearning": {
        "method": "POST",
        "url": "/api/learning/{id}/bookmark",
        "auth": "required"
      },
      "removeBookmark": {
        "method": "DELETE",
        "url": "/api/learning/{id}/bookmark",
        "auth": "required"
      },
      "getBookmarks": {
        "method": "GET",
        "url": "/api/learning/bookmarks",
        "auth": "required"
      },
      "markAsCompleted": {
        "method": "POST",
        "url": "/api/learning/{id}/complete",
        "auth": "required"
      },
      "updateProgress": {
        "method": "PUT",
        "url": "/api/learning/{id}/progress",
        "auth": "required",
        "body": {
          "readingProgress": "number"
        }
      },
      "getUserProgress": {
        "method": "GET",
        "url": "/api/learning/progress",
        "auth": "required",
        "query": {
          "page": "number",
          "limit": "number",
          "completed": "boolean"
        }
      }
    }
  }
}